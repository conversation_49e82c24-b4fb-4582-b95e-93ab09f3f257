include $(TOPDIR)/rules.mk

PKG_NAME := smart_qos
PKG_VERSION := 1.0
PKG_RELEASE := 1
PKG_BUILD_DIR := $(BUILD_DIR)/$(PKG_NAME)

include $(INCLUDE_DIR)/package.mk

define Package/$(PKG_NAME)
	CATEGORY := Utilities
	TITLE := VSOL smart qos
	DEPENDS := +libblobmsg-json +libjson-c +libubus +libubox +libuci +vsuci
endef

define Package/$(PKG_NAME)/description
	VSOL smart qos
endef


define Build/Compile
$(MAKE) -C $(PKG_BUILD_DIR) \
	CC="$(TARGET_CC)" \
	CFLAGS="$(TARGET_CFLAGS) $(SMART_QOS_CONFIGS) -g -flto -Wall" \
	LDFLAGS="$(TARGET_LDFLAGS)"
endef

define Package/$(PKG_NAME)/install
	$(INSTALL_DIR) $(1)/etc/config
	$(INSTALL_DIR) $(1)/etc/init.d
	$(INSTALL_DIR) $(1)/usr/bin
	$(INSTALL_BIN) ./files/smart_qos.conf $(1)/etc/config/smart_qos
	$(INSTALL_BIN) ./files/smart_qos $(1)/etc/init.d/smart_qos
	$(INSTALL_BIN) $(PKG_BUILD_DIR)/smart_qos $(1)/usr/bin/
endef

$(eval $(call BuildPackage,$(PKG_NAME)))
