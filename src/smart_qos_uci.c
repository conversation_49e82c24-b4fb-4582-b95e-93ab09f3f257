#include "smart_qos_uci.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <syslog.h>

int smart_qos_read_uci_config(smart_qos_config_t *config)
{
    config->enable = vsuci_get_int32("smart_qos", "smart_qos", "enable");
    config->mode = vsuci_get_int32("smart_qos", "smart_qos", "mode");

    return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_update_uci_config(const smart_qos_config_t *config)
{
    vsuci_set_int32((int)config->enable, "smart_qos", "smart_qos", "enable");
    vsuci_set_int32((int)config->mode, "smart_qos", "smart_qos", "mode");

    return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_read_custom_protocol_uci(char *protocol, int size)
{
    if (vsuci_get("smart_qos", "smart_qos", "custom_protocol", protocol, size) != 0)
        return SMART_QOS_UBUS_RET_FAIL;
    else
        return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_read_web_protocol_uci(char *protocol, int size)
{
    if (vsuci_get("smart_qos", "smart_qos", "web_protocol", protocol, size) != 0)
        return SMART_QOS_UBUS_RET_FAIL;
    else
        return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_read_game_protocol_uci(char *protocol, int size)
{
    if (vsuci_get("smart_qos", "smart_qos", "game_protocol", protocol, size) != 0)
        return SMART_QOS_UBUS_RET_FAIL;
    else
        return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_read_video_protocol_uci(char *protocol, int size)
{
    if (vsuci_get("smart_qos", "smart_qos", "video_protocol", protocol, size) != 0)
        return SMART_QOS_UBUS_RET_FAIL;
    else
        return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_read_social_protocol_uci(char *protocol, int size)
{
    if (vsuci_get("smart_qos", "smart_qos", "social_protocol", protocol, size) != 0)
        return SMART_QOS_UBUS_RET_FAIL;
    else
        return SMART_QOS_UBUS_RET_SUCC;
}
