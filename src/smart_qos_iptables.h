
#ifndef __SMART_QOS_IPTABLES_H__
#define __SMART_QOS_IPTABLES_H__

#include "smart_qos_util.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <syslog.h>

int smart_qos_iptables_init(void);
int os_execcmd(char *pc_command);
unsigned int iptables_insrule(char iptables_ver, char *pc_t, char *pc_chain, char *pc_rule);
unsigned int iptables_rule_op(char iptables_ver, const char *table, const char *act, const char *chain, const char *rule);
unsigned int iptables_flushchain(char iptables_ver, char *pc_t, char *pc_c);
int iptables_mangle_rule_exist(const char *chain, const char *target);
int iptables_mangle_chain_exists(const char *chain);
int ip6tables_mangle_rule_exist(const char *chain, const char *target);
int ip6tables_mangle_chain_exists(const char *chain);
void smart_qos_iptables_flush_rule(void);
void smart_qos_iptables_protocol_priority_rule(char *protocol);

#endif //__SMART_QOS_IPTABLES_H__
