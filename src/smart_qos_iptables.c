#include "smart_qos_iptables.h"

int os_execcmd(char *pc_command)
{
    int pid = 0;
    int status = 0;
    int *argv[] = {"sh", "-c", pc_command, NULL};

    if (pc_command == NULL)
    {
        return -1;
    }

    pid = fork();
    if (pid < 0)
    {
        return -1;
    }
    else if (pid == 0)
    {
        // 子进程执行分支
        execv("/bin/sh", argv);
        _exit(127);
    }

    /* wait for child process return */
    while (waitpid(pid, &status, 0) < 0)
    {
        if (errno != (unsigned int)EINTR)
        {
            return -1;
        }
    }

    return WIFEXITED(status) ? (0) : (-1);
}

//insert rule to the first line
unsigned int iptables_insert_rule(char iptables_ver, char *pc_t, char *pc_chain, char *pc_rule)
{
    char ac_cmd[512] = {0};

    snprintf(ac_cmd, sizeof(ac_cmd), "%s -w -t %s -I %s %s > /dev/null",
             (iptables_ver == 4) ? "iptables" : "ip6tables", pc_t, pc_chain, pc_rule);
    os_execcmd(ac_cmd);

    return 0;
}

//add rule to the last line
unsigned int iptables_add_rule(char iptables_ver, char *pc_t, char *pc_chain, char *pc_rule)
{
    char ac_cmd[512] = {0};

    snprintf(ac_cmd, sizeof(ac_cmd), "%s -w -t %s -A %s %s > /dev/null",
             (iptables_ver == 4) ? "iptables" : "ip6tables", pc_t, pc_chain, pc_rule);
    os_execcmd(ac_cmd);

    return 0;
}

unsigned int iptables_rule_op(char iptables_ver, const char *table, const char *act, const char *chain, const char *rule)
{
    char cmd_buf[512] = {0};

    snprintf(cmd_buf, sizeof(cmd_buf), "%s -w -t %s %s %s %s > /dev/null 2>&1", (iptables_ver == 4) ? "iptables" : "ip6tables", table, act, chain, rule);
    os_execcmd(cmd_buf);
    return 0;
}

unsigned int iptables_flush_chain(char iptables_ver, char *pc_t, char *pc_c)
{
    char  ac_cmd[512] = {0};

    snprintf(ac_cmd, sizeof(ac_cmd), "%s -w -t %s -F %s > /dev/null 2>&1",
                     (iptables_ver == 4) ? "iptables" : "ip6tables", pc_t, pc_c);
    os_execcmd(ac_cmd);
    return 0;
}

int iptables_mangle_rule_exist(const char *chain, const char *target)
{
    char cmd[256] = {0};

    snprintf(cmd, sizeof(cmd), "iptables -t mangle -C %s %s >/dev/null 2>&1", chain, target);
    if (system(cmd) == 0)
        return 1; // 存在，返回1
    else
        return 0; // 不存在，返回0
}

int iptables_mangle_chain_exists(const char *chain)
{
    char cmd[256] = {0};

    snprintf(cmd, sizeof(cmd), "iptables -t mangle -L %s >/dev/null 2>&1", chain);
    if (system(cmd) == 0)
        return 1; // 存在，返回1
    else
        return 0; // 不存在，返回0
}

int ip6tables_mangle_rule_exist(const char *chain, const char *target)
{
    char cmd[256] = {0};

    snprintf(cmd, sizeof(cmd), "ip6tables -t mangle -C %s %s >/dev/null 2>&1", chain, target);
    if (system(cmd) == 0)
        return 1; // 存在，返回1
    else
        return 0; // 不存在，返回0
}

int ip6tables_mangle_chain_exists(const char *chain)
{
    char cmd[256] = {0};

    snprintf(cmd, sizeof(cmd), "ip6tables -t mangle -L %s >/dev/null 2>&1", chain);
    if (system(cmd) == 0)
        return 1; // 存在，返回1
    else
        return 0; // 不存在，返回0
}

int smart_qos_iptables_init(void)
{
    /* 由于dpi需要分析连续的多个数据包，因此数据包走加速器之前需确保数据包先进入内核，然后再学习和加速，这里设置加速之前的学习数量为5 */
    os_execcmd("echo 5 > /proc/hsan/cfe/l4/lrn_pkt_num");

    /* ipv4 */
    if (iptables_mangle_chain_exists("smart_qos_chain") == 0)
    {
        printf("iptables mangle chain smart_qos_chain not found, creating it...\n");
        iptables_rule_op(4, "mangle", "-N", "smart_qos_chain", "");
    }

    if (iptables_mangle_rule_exist("FORWARD", "-j smart_qos_chain") == 0)
    {
        printf("iptables mangle adding rule: smart_qos_chain to FORWARD\n");
        iptables_add_rule(4, "mangle", "FORWARD", "-j smart_qos_chain");
    }

    /* ipv6 */
    if (ip6tables_mangle_chain_exists("smart_qos_chain") == 0)
    {
        printf("ip6tables mangle chain smart_qos_chain not found, creating it...\n");
        iptables_rule_op(6, "mangle", "-N", "smart_qos_chain", "");
    }

    if (ip6tables_mangle_rule_exist("FORWARD", "-j smart_qos_chain") == 0)
    {
        printf("ip6tables mangle adding rule: smart_qos_chain to FORWARD\n");
        iptables_add_rule(6, "mangle", "FORWARD", "-j smart_qos_chain");
    }

    return 0;
}

void smart_qos_iptables_flush_rule(void)
{
    iptables_flush_chain(4, "mangle", "smart_qos_chain");
    iptables_flush_chain(6, "mangle", "smart_qos_chain");
}

void smart_qos_iptables_protocol_priority_rule(char *protocol)
{
    char rule_buf[2048] = {0};

    if (protocol == NULL)
        return;
    if (strcmp(protocol, "") == 0)
        return;

    snprintf(rule_buf, sizeof(rule_buf), "-i br-lan  -m ndpi --proto %s -j cfe --fwd accept --acc yes --qid 0 --pq 7", protocol);
    iptables_add_rule(4, "mangle", "smart_qos_chain", rule_buf);
    iptables_add_rule(6, "mangle", "smart_qos_chain", rule_buf);
    snprintf(rule_buf, sizeof(rule_buf), "-o br-lan  -m ndpi --proto %s -j cfe --fwd accept --acc yes --qid 0 --pq 7", protocol);
    iptables_add_rule(4, "mangle", "smart_qos_chain", rule_buf);
    iptables_add_rule(6, "mangle", "smart_qos_chain", rule_buf);
}
