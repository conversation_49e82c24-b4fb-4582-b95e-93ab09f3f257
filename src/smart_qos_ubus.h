
#ifndef __SMART_QOS_UBUS_H__
#define __SMART_QOS_UBUS_H__

#include "smart_qos_uci.h"
#include "smart_qos_util.h"
#include "smart_qos_iptables.h"

void app_reply_ubus_fail(struct ubus_context *ctx, struct ubus_request_data *req, const char *reason,
                            uint32_t error_code);

void app_reply_ubus_succ(struct ubus_context *ctx, struct ubus_request_data *req);

int32_t app_check_ubus_params(struct ubus_context *ctx, struct ubus_request_data *req, struct blob_attr **attr,
                                 uint32_t attr_len, const struct blobmsg_policy *policy, uint32_t policy_len);

int smart_qos_init(struct ubus_context *ctx, struct ubus_object *obj,
                     struct ubus_request_data *req, const char *method,
                     struct blob_attr *msg);

/* UCI operation functions */
int smart_qos_read_uci_config(smart_qos_config_t *config);
int smart_qos_update_uci_config(const smart_qos_config_t *config);

/* Smart QoS action functions */
int smart_qos_set_enable_action(int enable);
int smart_qos_set_mode_action(int mode);
int smart_qos_apply_config(const smart_qos_config_t *config);


#endif //__SMART_QOS_UBUS_H__
