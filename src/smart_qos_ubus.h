
#ifndef __SMART_QOS_UBUS_H__
#define __SMART_QOS_UBUS_H__

#include <libubox/blobmsg_json.h>
#include <libubus.h>
#include <libubox/uloop.h>

enum smart_qos_param_e{
    SMART_QOS_UBUS_ATTR0 = 0,
    SMART_QOS_UBUS_ATTR1,
    SMART_QOS_UBUS_ATTR2,
    SMART_QOS_UBUS_ATTR3,
    SMART_QOS_UBUS_ATTR4,
    SMART_QOS_UBUS_ATTR5,
    SMART_QOS_UBUS_ATTR_MAX,
};

typedef enum {
    SMART_QOS_UBUS_RET_SUCC = 0,               /* Return success */
    SMART_QOS_UBUS_RET_FAIL = -1,              /* General error. */
    SMART_QOS_UBUS_RET_NULLPTR = -2,           /* The input pointer is null. */
    SMART_QOS_UBUS_RET_PARAM_INVALID = -3,     /* Invalid parameter. */
} app_ret_e;
    
void app_reply_ubus_fail(struct ubus_context *ctx, struct ubus_request_data *req, const char *reason,
                            uint32_t error_code);

void app_reply_ubus_succ(struct ubus_context *ctx, struct ubus_request_data *req);

int32_t app_check_ubus_params(struct ubus_context *ctx, struct ubus_request_data *req, struct blob_attr **attr,
                                 uint32_t attr_len, const struct blobmsg_policy *policy, uint32_t policy_len);

int smart_qos_init(struct ubus_context *ctx, struct ubus_object *obj,
                     struct ubus_request_data *req, const char *method,
                     struct blob_attr *msg);

                     
#endif //__SMART_QOS_UBUS_H__
