#include "smart_qos_ubus.h"

void app_reply_ubus_fail(struct ubus_context *ctx, struct ubus_request_data *req, const char *reason,
                            uint32_t error_code)
{
    struct blob_buf result_buff = {0};
    blob_buf_init(&result_buff, 0);
    blobmsg_add_u32(&result_buff, "result", error_code);
    blobmsg_add_string(&result_buff, "fail_reason", reason);
    ubus_send_reply(ctx, req, result_buff.head);
    if (result_buff.buf != NULL) {
        blob_buf_free(&result_buff);
    }
}

void app_reply_ubus_succ(struct ubus_context *ctx, struct ubus_request_data *req)
{
    struct blob_buf result_buff = {0};
    blob_buf_init(&result_buff, 0);
    blobmsg_add_u32(&result_buff, "result", 0);
    ubus_send_reply(ctx, req, result_buff.head);
    if (result_buff.buf != NULL) {
        blob_buf_free(&result_buff);
    }
}

int32_t app_check_ubus_params(struct ubus_context *ctx, struct ubus_request_data *req, struct blob_attr **attr,
                                 uint32_t attr_len, const struct blobmsg_policy *policy, uint32_t policy_len)
{
    char fail_reason[128] = {0};
    for (uint32_t i = 0; i < attr_len; i++) {
        if (attr[i] == NULL) {
            if (snprintf(fail_reason, sizeof(fail_reason), "Parameter %s is NULL. Please try again.",
                          policy[i].name) < 0) {
                return SMART_QOS_UBUS_RET_PARAM_INVALID;
            }
            app_reply_ubus_fail(ctx, req, fail_reason, SMART_QOS_UBUS_RET_PARAM_INVALID);
            return SMART_QOS_UBUS_RET_PARAM_INVALID;
        }
    }
    return SMART_QOS_UBUS_RET_SUCC;
}

int smart_qos_init(struct ubus_context *ctx, struct ubus_object *obj,
                     struct ubus_request_data *req, const char *method,
                     struct blob_attr *msg)
{

    app_reply_ubus_succ(ctx, req);
    return UBUS_STATUS_OK;
}

static struct blobmsg_policy smart_qos_set[] = {
    [SMART_QOS_UBUS_ATTR0]= { .name = "enable",    .type = BLOBMSG_TYPE_INT32 },
    [SMART_QOS_UBUS_ATTR1]= { .name = "mode",      .type = BLOBMSG_TYPE_INT32 },
};

int smart_qos_set_handler(struct ubus_context *ctx, 
                                     struct ubus_object *obj,
                                     struct ubus_request_data *req,
                                     const char *method, 
                                     struct blob_attr *msg)
{
    int lRet = 0;

    /* todo */
    
    if (lRet != 0 ) {
        app_reply_ubus_fail(ctx, req,"smart_qos_set_handler error", lRet);
        return UBUS_STATUS_OK;
    }
    app_reply_ubus_succ(ctx, req);
    return UBUS_STATUS_OK;
}


static const struct ubus_method smart_qos_methods[] = {
    UBUS_METHOD_NOARG("smart_qos_init", smart_qos_init),
    UBUS_METHOD("smart_qos_set", smart_qos_set_handler, smart_qos_set),
};

static struct ubus_object_type smart_qos_ubus_main_object_type =
    UBUS_OBJECT_TYPE("smart_qos", smart_qos_methods);

struct ubus_object app_urlfilter_ubus_object = {
    .name = "smart_qos",
    .type = &smart_qos_ubus_main_object_type,
    .methods = smart_qos_methods,
    .n_methods = ARRAY_SIZE(smart_qos_methods),
};

static void smart_qos_ubus_run(void)
{
    static struct ubus_context *app_ubus_ctx = NULL;
    
    uloop_init();
    app_ubus_ctx = ubus_connect(NULL);
    
    ubus_add_uloop(app_ubus_ctx);

    if (ubus_add_object(app_ubus_ctx, &app_urlfilter_ubus_object))
    {
        return;
    }

    uloop_run();
    
    if (app_ubus_ctx)
    {
        ubus_free(app_ubus_ctx);
    }
    uloop_done();
}

int main(int argc, char *argv[])
{
    smart_qos_ubus_run();
    return 0;
}