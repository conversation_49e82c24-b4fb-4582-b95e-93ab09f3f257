
#ifndef __SMART_QOS_UCI_H__
#define __SMART_QOS_UCI_H__

#include "smart_qos_util.h"

int smart_qos_read_uci_config(smart_qos_config_t *config);
int smart_qos_update_uci_config(const smart_qos_config_t *config);
int smart_qos_read_custom_protocol_uci(char *protocol, int size);
int smart_qos_read_web_protocol_uci(char *protocol, int size);
int smart_qos_read_game_protocol_uci(char *protocol, int size);
int smart_qos_read_video_protocol_uci(char *protocol, int size);
int smart_qos_read_social_protocol_uci(char *protocol, int size);

#endif //__SMART_QOS_UCI_H__
