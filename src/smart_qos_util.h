#ifndef SMART_QOS_UTIL
#define SMART_QOS_UTIL

#include <libubox/blobmsg_json.h>
#include <libubus.h>
#include <libubox/uloop.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <syslog.h>
#include <errno.h>
#include <signal.h>
#include <uci.h>

enum smart_qos_enable_e{
    SMART_QOS_DISABLE = 0,
    SMART_QOS_ENABLE = 1,
};

enum smart_qos_mode_e{
    CUSTOM_PRIORITY_MODE    = 0,
    WEB_PRIORITY_MODE       = 1,
    GAME_PRIORITY_MODE      = 2,
    VIDEO_PRIORITY_MODE     = 3,
    SOCIAL_PRIORITY_MODE    = 4,
};

enum smart_qos_param_e{
    SMART_QOS_UBUS_ATTR0 = 0,
    SMART_QOS_UBUS_ATTR1,
    SMART_QOS_UBUS_ATTR2,
    SMART_QOS_UBUS_ATTR3,
    SMART_QOS_UBUS_ATTR4,
    SMART_QOS_UBUS_ATTR5,
    SMART_QOS_UBUS_ATTR_MAX,
};

typedef enum {
    SMART_QOS_UBUS_RET_SUCC = 0,               /* Return success */
    SMART_QOS_UBUS_RET_FAIL = -1,              /* General error. */
    SMART_QOS_UBUS_RET_NULLPTR = -2,           /* The input pointer is null. */
    SMART_QOS_UBUS_RET_PARAM_INVALID = -3,     /* Invalid parameter. */
} smart_qos_ret_e;

typedef struct {
    int enable;
    int mode;
} smart_qos_config_t;

#endif
