#!/bin/sh /etc/rc.common
# (C) 2013 openwrt.org

START=99

USE_PROCD=1
NAME="smart_qos"
PROG="/usr/bin/smart_qos"

validate_auto_wan_section() {
    uci_load_validate smart_qos smart_qos "$1" "$2" \
        'enable:int' \
        'mode:int'
}

reload_service() {
    stop
    start
}

service_triggers() {
    procd_add_reload_trigger "smart_qos"
    procd_open_validate
    procd_add_validation validate_auto_wan_section
    procd_close_validate
}

start_service() {
	local enable

	config_load smart_qos
	config_get enable smart_qos enable

	if [ "enable" = "0" ]; then
		return 1
	fi
	procd_open_instance
	procd_set_param command "$PROG"
	procd_set_param respawn
	procd_set_param stderr 1
	procd_set_param stdout 1
	procd_close_instance
}

